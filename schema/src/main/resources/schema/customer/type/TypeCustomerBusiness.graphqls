type CustomerBusiness implements Customer {
    id: ID!
    type: CustomerType!
    company: Company!
    status: CustomerStatus!
    stage: CustomerStage!
    basicDetails: BusinessCustomerBasicDetails!
    documents: [Document]!
    assignments: [CustomerAssignment]!
    notes: [Notes]
    customTags: [CustomTag]!
}

type BusinessCustomerBasicDetails {
    legalName: String!
    website: String!
    size: EntitySize!
    industry: String
    referralSource: String
    address: String
    contactDetails: ContactDetails!
}
