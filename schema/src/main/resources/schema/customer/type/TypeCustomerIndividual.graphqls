type CustomerIndividual implements Customer {
    id: ID!
    company: Company!
    type: CustomerType!
    status: CustomerStatus!
    stage: CustomerStage!
    basicDetails: IndividualCustomerBasicDetails!
    documents: [Document]!
    assignments: [CustomerAssignment]!
    notes: [Notes]
    customTags: [CustomTag]!
}

type IndividualCustomerBasicDetails {
    contactDetails: ContactDetails!
    referralSource: String
    address: String
}
