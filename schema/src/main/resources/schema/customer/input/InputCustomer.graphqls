input CustomerCreateBasicDetailsInput {
    customerType: CustomerType!
    customerStage: CustomerStage!
    individualCustomerDetails: IndividualCustomerBasicDetailsInput
    businessCustomerDetails: BusinessCustomerBasicDetailsInput
}

input CustomerCreateAdditionalInformationInput {
    customerId: ID!
    assignments: [AssignmentInput!]
    notes: [NotesInput]
    documents: [DocumentInput!]
    customTags: [CustomTagInput!]
}

input CustomerBasicDetailsUpdateInput {
    id: ID!
    customerType: CustomerType!
    customerStage: CustomerStage!
    # code will validate for either of it to be present
    individualCustomerBasicDetailsInput: IndividualCustomerBasicDetailsInput
    businessCustomerBasicDetailsInput: BusinessCustomerBasicDetailsInput
}

input CustomerUpdateAdditionalInformationInput {
    assignments: [AssignmentInput!]
    notes: [NotesInput]
    documents: [DocumentInput!]
    customTags: [CustomTagInput!]
}

# Company user ids
input AssignmentInput { 
    accountManager: ID
    supportRepresentative: ID
}

input ContactPersonDetailsInput {
    name: String!
    title: String
    email: String!
    phoneNo: String
    contactType: ContactType!
}