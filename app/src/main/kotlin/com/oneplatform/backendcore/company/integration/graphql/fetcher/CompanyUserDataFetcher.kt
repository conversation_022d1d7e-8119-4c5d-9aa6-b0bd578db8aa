package com.oneplatform.backendcore.company.integration.graphql.fetcher

import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsData
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment
import com.netflix.graphql.dgs.DgsQuery
import com.oneplatform.backendcore.DgsConstants
import com.oneplatform.backendcore.company.integration.graphql.mapper.CompanyUserMapper
import com.oneplatform.backendcore.company.integration.service.CompanyUserService
import com.oneplatform.backendcore.core.auth.AuthenticatedUser
import com.oneplatform.backendcore.types.Company
import com.oneplatform.backendcore.types.CompanyUser
import io.github.oshai.kotlinlogging.KotlinLogging

private val log = KotlinLogging.logger {}

@DgsComponent
class CompanyUserDataFetcher(
    private val companyUserService: CompanyUserService,
    private val companyUserMapper: CompanyUserMapper,
    private val authUser: AuthenticatedUser
) {

    @DgsQuery(field = DgsConstants.QUERY.GetCompanyUser)
    fun getCompanyUser(): CompanyUser {
        log.info { "Fetching company user data" }
        val domainCompanyUser = companyUserService.getCompanyUserForEmail(authUser)
        return companyUserMapper.toGraphQL(domainCompanyUser)
    }

    @DgsData(parentType = DgsConstants.COMPANY.TYPE_NAME, field = DgsConstants.COMPANY.Users)
    fun companyUsers(dfe: DgsDataFetchingEnvironment): List<CompanyUser> {
        val company: Company = dfe.getSource()
        log.info { "Fetching users for company ${company.id}" }
        val domainUsers = companyUserService.getUsersForCompany(company.id, authUser)
        return companyUserMapper.toGraphQL(domainUsers!!)
    }
}
