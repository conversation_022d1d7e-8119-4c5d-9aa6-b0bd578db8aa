package com.oneplatform.backendcore.customer.integration.db.service

import com.oneplatform.backendcore.customer.domain.model.customer.Customer
import com.oneplatform.backendcore.customer.domain.model.customer.IndividualCustomer
import com.oneplatform.backendcore.customer.domain.repository.IndividualCustomerRepository
import com.oneplatform.backendcore.customer.integration.db.mapper.toDomain
import com.oneplatform.backendcore.customer.integration.db.mapper.toEntity
import com.oneplatform.backendcore.customer.integration.db.repository.IndividualCustomerJpaRepository
import com.oneplatform.backendcore.customer.integration.db.service.specutil.toIndividualCustomerSpec
import com.oneplatform.backendcore.types.GetCustomersQueryFilterInput
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Component
class IndividualCustomerRepositoryService(
    private val individualCustomerJpaRepository: IndividualCustomerJpaRepository
) : IndividualCustomerRepository {

    override fun save(
        customer: IndividualCustomer
    ): IndividualCustomer {
        log.info { "Saving individual customer details" }
        val entity = customer.toEntity()
        val savedEntity = individualCustomerJpaRepository.save(entity).also {
            log.info { "Individual customer details entity created=${it.id}" }
        }
        return savedEntity.toDomain()
    }

    override fun findAllWithSpec(filter: GetCustomersQueryFilterInput, companyId: String): List<Customer> {
        log.info { "Fetching individual customers for filter=$filter and companyId: $companyId" }
        return individualCustomerJpaRepository.findAll(filter.toIndividualCustomerSpec(companyId)).map { it.toDomain() }
    }

    override fun findById(id: String): Customer? {
        log.info { "Finding individual customer by id=$id" }
        return individualCustomerJpaRepository.findById(id).orElse(null)?.toDomain()
    }
}
