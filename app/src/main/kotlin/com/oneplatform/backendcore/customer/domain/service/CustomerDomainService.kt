package com.oneplatform.backendcore.customer.domain.service

import com.oneplatform.backendcore.customer.domain.service.CoreCustomerDomainService
import com.oneplatform.backendcore.customer.domain.model.customer.BusinessCustomer
import com.oneplatform.backendcore.customer.domain.model.customer.Customer
import com.oneplatform.backendcore.customer.domain.model.customer.IndividualCustomer
import com.oneplatform.backendcore.customer.domain.request.CreateCustomerDomainReq
import com.oneplatform.backendcore.types.GetCustomersQueryFilterInput
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

private val log = KotlinLogging.logger {}

@Service
class CustomerDomainService(
    private val coreCustomerDomainService: CoreCustomerDomainService,
    private val customerDetailsDomainService: CustomerDetailsDomainService
) {

    @Transactional
    fun createCustomer(domainReq: CreateCustomerDomainReq): Customer {
        log.info { "Creating customer for company=${domainReq.company.id}" }
        if (domainReq.individualCustomer != null) {
            log.info { "Customer type is individual" }
            return createIndividualCustomer(domainReq)
        } else {
            log.info { "Customer type is business" }
            return createBusinessCustomer(domainReq)
        }
    }

    fun getCustomersForCompanyIdWithBasicDetails(filters: GetCustomersQueryFilterInput, id: String): List<Customer> {
        return customerDetailsDomainService.getCustomers(filters, id)
    }

    fun getCustomerById(id: String): Customer? {
        log.info { "Getting customer by id=$id" }
        return customerDetailsDomainService.getCustomerById(id)
    }

    private fun createIndividualCustomer(domainReq: CreateCustomerDomainReq): IndividualCustomer {
        val company = domainReq.company
        val customerDetails = domainReq.individualCustomer!!

        val createdNotes = coreCustomerDomainService.createNotes(company.id, customerDetails.details.notes)
        log.info { "Created Notes with id=${createdNotes?.id}" }

        val createdDocuments = coreCustomerDomainService.createDocuments(company.id, customerDetails.details.documents)
        log.info { "Created Documents with ids=${createdDocuments.map { it.id }}" }

        val createdCustomTags =
            coreCustomerDomainService.createCustomTags(company.id, customerDetails.details.customTags)
        log.info { "Created Custom Tags with ids=${createdCustomTags.map { it.id }}" }

        return customerDetailsDomainService.createIndividualCustomer(
            customerDetails,
            createdNotes,
            createdDocuments,
            createdCustomTags
        ).also {
            log.info { "Created Individual Customer Details with id=${it.id}" }
        }
    }


    private fun createBusinessCustomer(domainReq: CreateCustomerDomainReq): BusinessCustomer {
        val company = domainReq.company
        val customerDetails = domainReq.businessCustomer!!

        val createdNotes = coreCustomerDomainService.createNotes(company.id, customerDetails.details.notes)
        log.info { "Created Notes with id=${createdNotes?.id}" }

        val createdDocuments = coreCustomerDomainService.createDocuments(company.id, customerDetails.details.documents)
        log.info { "Created Documents with ids=${createdDocuments.map { it.id }}" }

        val createdCustomTags = coreCustomerDomainService.createCustomTags(company.id, customerDetails.details.customTags)
        log.info { "Created Custom Tags with ids=${createdCustomTags.map { it.id }}" }


        return customerDetailsDomainService.createBusinessCustomer(
            customerDetails,
            createdNotes,
            createdDocuments,
            createdCustomTags
        ).also {
            log.info { "Created Individual Customer Details with id=${it.id}" }
        }
    }
}
