package com.oneplatform.backendcore.customer.domain.model.customer

import com.oneplatform.backendcore.core.integration.mapper.toGraphQL
import com.oneplatform.backendcore.types.CustomerBusiness
import com.oneplatform.backendcore.types.CustomerStage
import com.oneplatform.backendcore.types.CustomerStatus
import com.oneplatform.backendcore.types.CustomerType

data class BusinessCustomer(
    val id: String? = null,
    val companyId: String,
    val details: BusinessCustomerDetails, // will be fetched seprately at the time of fetching
    val status: CustomerStatus,
    val stage: CustomerStage? = CustomerStage.LEAD
) : Customer {

    override fun getCustomerType(): CustomerType {
        return CustomerType.BUSINESS
    }
}

fun BusinessCustomer.toGraphQL(): CustomerBusiness {
    return CustomerBusiness(
        id = this.id ?: run { "" },
        type = this.getCustomerType(),
        company = com.oneplatform.backendcore.types.Company(
            id = this.companyId
        ),
        status = this.status,
        stage = this.stage!!,
        basicDetails = this.details.basicDetails.toGraphQL(),
        documents = this.details.documents.map { it.toGraphQL() },
        assignments = this.details.assignments.map { it.toGraphQL() },
        notes = this.details.notes.map { it.toGraphQL() },
        customTags = this.details.customTags.map { it.toGraphQL() }
    )
}