package com.oneplatform.backendcore.customer.integration.db.mapper

import com.oneplatform.backendcore.core.domain.model.customtag.CustomTag
import com.oneplatform.backendcore.core.domain.model.document.Document
import com.oneplatform.backendcore.core.domain.model.notes.Notes
import com.oneplatform.backendcore.customer.domain.model.customer.IndividualCustomer
import com.oneplatform.backendcore.customer.domain.model.customer.IndividualCustomerBasicDetails
import com.oneplatform.backendcore.customer.domain.model.customer.IndividualCustomerDetails
import com.oneplatform.backendcore.customer.integration.db.entity.JpaIndividualCustomerEntity
import com.oneplatform.backendcore.types.CustomerStatus

fun IndividualCustomer.toEntity(): JpaIndividualCustomerEntity {
    val details = this.details
    val basicDetails = details.basicDetails

    return JpaIndividualCustomerEntity(
        fullName = basicDetails.fullName,
        emailAddress = basicDetails.emailAddress,
        phoneNumber = basicDetails.phoneNumber,
        referralSource = basicDetails.referralSource,
        address = details.address,
        documentIds = details.documents.map { it.id },
        notesId = details.notes.mapNotNull { it.id },
        customTagIds = details.customTags.map { it.id ?: "" },
        assignments = details.assignments.map { it.toEntity() },
        companyId = this.companyId,
        customerType = this.getCustomerType(),
        status = this.status ?: CustomerStatus.ACTIVE,
        stage = this.stage,
    )
}

fun JpaIndividualCustomerEntity.toDomain(): IndividualCustomer {
    return IndividualCustomer(
        id = this.id,
        companyId = this.companyId,
        details = IndividualCustomerDetails(
            basicDetails = IndividualCustomerBasicDetails(
                fullName = this.fullName,
                emailAddress = this.emailAddress,
                phoneNumber = this.phoneNumber,
                referralSource = this.referralSource
            ),
            documents = this.documentIds.map { Document(it) },
            notes = this.notesId.map { Notes(id = it) },
            address = this.address,
            customTags = this.customTagIds.map { CustomTag(it) },
            assignments = this.assignments.map { it.toDomain() }
        ),
        status = this.status,
        stage = this.stage,
    )
}