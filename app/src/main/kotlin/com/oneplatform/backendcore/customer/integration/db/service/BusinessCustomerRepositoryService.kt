package com.oneplatform.backendcore.customer.integration.db.service

import com.oneplatform.backendcore.customer.domain.model.customer.BusinessCustomer
import com.oneplatform.backendcore.customer.domain.model.customer.Customer
import com.oneplatform.backendcore.customer.domain.repository.BusinessCustomerRepository
import com.oneplatform.backendcore.customer.integration.db.mapper.toDomain
import com.oneplatform.backendcore.customer.integration.db.mapper.toEntity
import com.oneplatform.backendcore.customer.integration.db.repository.BusinessCustomerJpaRepository
import com.oneplatform.backendcore.customer.integration.db.service.specutil.toBusinessCustomerSpec
import com.oneplatform.backendcore.types.GetCustomersQueryFilterInput
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Component
class BusinessCustomerRepositoryService(
    private val businessCustomerJpaRepository: BusinessCustomerJpaRepository
) : BusinessCustomerRepository {

    override fun save(
        customer: BusinessCustomer
    ): BusinessCustomer {
        log.info { "Saving business customer details" }
        val entity = customer.toEntity()
        val savedEntity = businessCustomerJpaRepository.save(entity).also {
            log.info { "Business customer details entity created=${it.id}" }
        }
        return savedEntity.toDomain()
    }

    override fun findAllWithSpec(filter: GetCustomersQueryFilterInput, companyId: String): List<Customer> {
        log.info { "Fetching business customers for filter=$filter and companyId: $companyId" }
        return businessCustomerJpaRepository.findAll(filter.toBusinessCustomerSpec(companyId)).map { it.toDomain() }
    }

    override fun findById(id: String): Customer? {
        log.info { "Finding business customer by id=$id" }
        return businessCustomerJpaRepository.findById(id).orElse(null)?.toDomain()
    }
}
