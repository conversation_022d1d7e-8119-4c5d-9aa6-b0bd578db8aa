package com.oneplatform.backendcore.customer.integration

import com.oneplatform.backendcore.customer.domain.model.ContactDetails
import com.oneplatform.backendcore.customer.domain.model.customer.BusinessCustomerBasicDetails
import com.oneplatform.backendcore.customer.domain.model.customer.BusinessCustomerDetails
import com.oneplatform.backendcore.customer.domain.model.customer.IndividualCustomerBasicDetails
import com.oneplatform.backendcore.customer.domain.model.customer.IndividualCustomerDetails
import com.oneplatform.backendcore.types.CustomerCreateBasicDetailsInput

fun CustomerCreateBasicDetailsInput.createBusinessCustomerDetails(): BusinessCustomerDetails {
    val basicDetails = this.businessCustomerDetails!!
    return BusinessCustomerDetails(
        basicDetails = BusinessCustomerBasicDetails(
            legalName = basicDetails.legalName,
            website = basicDetails.website,
            size = basicDetails.size,
            industry = basicDetails.industry,
            referralSource = basicDetails.referralSource,
            contactDetails = ContactDetails(
                contactType = basicDetails.contactPersonDetails.contactType,
                name = basicDetails.contactPersonDetails.name,
                title = basicDetails.contactPersonDetails.title,
                email = basicDetails.contactPersonDetails.email,
                phoneNo = basicDetails.contactPersonDetails.phoneNo
            )
        )
    )
}

fun CustomerCreateBasicDetailsInput.createIndividualCustomerDetails(): IndividualCustomerDetails {
    val basicDetails = this.individualCustomerDetails!!
    return IndividualCustomerDetails(
        basicDetails = IndividualCustomerBasicDetails(
            referralSource = basicDetails.referralSource,
            fullName = basicDetails.fullName,
            emailAddress = basicDetails.emailAddress,
            phoneNumber = basicDetails.phoneNumber
        )
    )
}