package com.oneplatform.backendcore.customer.integration.db.mapper

import com.oneplatform.backendcore.core.domain.model.customtag.CustomTag
import com.oneplatform.backendcore.core.domain.model.document.Document
import com.oneplatform.backendcore.core.domain.model.notes.Notes
import com.oneplatform.backendcore.customer.domain.model.ContactDetails
import com.oneplatform.backendcore.customer.domain.model.customer.BusinessCustomer
import com.oneplatform.backendcore.customer.domain.model.customer.BusinessCustomerBasicDetails
import com.oneplatform.backendcore.customer.domain.model.customer.BusinessCustomerDetails
import com.oneplatform.backendcore.customer.integration.db.entity.JpaBusinessCustomerEntity

fun BusinessCustomer.toEntity(): JpaBusinessCustomerEntity {
    val details = this.details
    val basicDetails = details.basicDetails

    return JpaBusinessCustomerEntity(
        legalName = basicDetails.legalName,
        website = basicDetails.website,
        size = basicDetails.size,
        industry = basicDetails.industry,
        contactDetailsEntity = basicDetails.contactDetails.toEntity(),
        referralSource = basicDetails.referralSource,
        address = details.address,
        documentIds = details.documents.map { it.id },
        notesId = details.notes.mapNotNull { it.id },
        customTagIds = details.customTags.map { it.id ?: "" },
        assignments = details.assignments.map { it.toEntity() },
        companyId = this.companyId,
        customerType = this.getCustomerType(),
        status = this.status,
        stage = this.stage,
    )
}

fun JpaBusinessCustomerEntity.toDomain(): BusinessCustomer {
    return BusinessCustomer(
        id = this.id,
        companyId = this.companyId,
        details = BusinessCustomerDetails(
            basicDetails = BusinessCustomerBasicDetails(
                legalName = this.legalName,
                website = this.website,
                size = this.size,
                industry = this.industry,
                referralSource = this.referralSource,
                contactDetails = this.contactDetailsEntity.toDomain()
            ),
            documents = this.documentIds.map { Document(id = it) },
            notes = this.notesId.map { Notes(id = it) },
            address = this.address,
            customTags = this.customTagIds.map { CustomTag(id = it) },
            assignments = this.assignments.map { it.toDomain() }
        ),
        status = this.status,
        stage = this.stage,
    )
}