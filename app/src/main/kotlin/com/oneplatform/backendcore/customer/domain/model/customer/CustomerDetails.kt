package com.oneplatform.backendcore.customer.domain.model.customer

import com.oneplatform.backendcore.core.domain.model.customtag.CustomTag
import com.oneplatform.backendcore.core.domain.model.document.Document
import com.oneplatform.backendcore.core.domain.model.notes.Notes
import com.oneplatform.backendcore.types.Company
import com.oneplatform.backendcore.types.CompanyUser
import com.oneplatform.backendcore.types.CompanyUserStatus
import com.oneplatform.backendcore.types.CustomerType


data class CustomerAssignment(
    val accountManagerID: String?,
    val supportRepresentativeID: String?
)

fun CustomerAssignment.toGraphQL(): com.oneplatform.backendcore.types.CustomerAssignment {
    return com.oneplatform.backendcore.types.CustomerAssignment(
        accountManager = this.accountManagerID?.let {
            CompanyUser(
                id = accountManagerID,
                name = "",
                email = "",
                roles = mutableListOf(),
                status = CompanyUserStatus.ACTIVE,
                company = Company(id = "")
            )
        },
        supportRepresentative = this.supportRepresentativeID?.let {
            CompanyUser(
                id = supportRepresentativeID,
                name = "",
                email = "",
                roles = mutableListOf(),
                status = CompanyUserStatus.ACTIVE,
                company = Company(id = "")
            )
        }
    )
}

data class IndividualCustomerDetails(
    val basicDetails: IndividualCustomerBasicDetails,
    val documents: List<Document> = mutableListOf(),
    val notes: List<Notes> = mutableListOf(),
    val customTags: List<CustomTag> = mutableListOf(),
    val assignments: List<CustomerAssignment> = mutableListOf()
) {
    val customerType: CustomerType = CustomerType.INDIVIDUAL
}


class BusinessCustomerDetails(
    val basicDetails: BusinessCustomerBasicDetails,
    val documents: List<Document> = mutableListOf(),
    val notes: List<Notes> = mutableListOf(),
    val customTags: List<CustomTag> = mutableListOf(),
    val assignments: List<CustomerAssignment> = mutableListOf()
) {
    val customerType: CustomerType = CustomerType.BUSINESS
}