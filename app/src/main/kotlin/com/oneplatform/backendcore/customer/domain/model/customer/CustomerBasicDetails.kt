package com.oneplatform.backendcore.customer.domain.model.customer

import com.oneplatform.backendcore.customer.domain.model.ContactDetails
import com.oneplatform.backendcore.types.ContactType
import com.oneplatform.backendcore.types.EntitySize

interface CustomerBasicDetails

data class IndividualCustomerBasicDetails(
    val fullName: String,
    val emailAddress: String,
    val phoneNumber: String?,
    val referralSource: String?,
    val address: String = "",
) : CustomerBasicDetails

fun IndividualCustomerBasicDetails.toGraphQL(): com.oneplatform.backendcore.types.IndividualCustomerBasicDetails {
    return com.oneplatform.backendcore.types.IndividualCustomerBasicDetails(
        referralSource = this.referralSource,
        contactDetails = com.oneplatform.backendcore.types.ContactDetails(
            contactType = ContactType.PERSON,
            name = this.fullName,
            title = "",
            email = this.emailAddress,
            phoneNo = this.phoneNumber,
            address = this.address
        )
    )
}

data class BusinessCustomerBasicDetails(
    val legalName: String,
    val website: String,
    val size: EntitySize,
    val industry: String?,
    val referralSource: String?,
    val contactDetails: ContactDetails,
) : CustomerBasicDetails

fun BusinessCustomerBasicDetails.toGraphQL(): com.oneplatform.backendcore.types.BusinessCustomerBasicDetails {
    return com.oneplatform.backendcore.types.BusinessCustomerBasicDetails(
        legalName = this.legalName,
        website = this.website,
        size = this.size,
        industry = this.industry,
        referralSource = this.referralSource,
        contactDetails = com.oneplatform.backendcore.types.ContactDetails(
            contactType = this.contactDetails.contactType,
            name = this.contactDetails.name,
            title = this.contactDetails.title,
            email = this.contactDetails.email,
            phoneNo = this.contactDetails.phoneNo,
            address = this.contactDetails.address
        ),
    )
}