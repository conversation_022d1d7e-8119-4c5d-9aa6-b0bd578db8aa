package com.oneplatform.backendcore.customer.integration.db.entity

import com.oneplatform.backendcore.types.CustomerStage
import com.oneplatform.backendcore.types.CustomerStatus
import com.oneplatform.backendcore.types.CustomerType
import jakarta.persistence.*
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.annotations.UuidGenerator
import org.hibernate.type.SqlTypes
import java.time.LocalDateTime

@Entity
@Table(name = "individual_customer", schema = "customer")
data class JpaIndividualCustomerEntity(
    @Id
    @UuidGenerator
    val id: String? = null,

    @Column(name = "company_id", nullable = false)
    val companyId: String,

    @Enumerated(EnumType.STRING)
    @Column(name = "customer_type", nullable = false)
    val customerType: CustomerType,

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    val status: CustomerStatus,

    @Enumerated(EnumType.STRING)
    @Column
    val stage: CustomerStage? = CustomerStage.LEAD,

    @Column(name = "full_name", nullable = false)
    val fullName: String,

    @Column(name = "email_address", nullable = false)
    val emailAddress: String,

    @Column(name = "phone_number", nullable = true)
    val phoneNumber: String? = null,

    @Column(name = "referral_source")
    val referralSource: String?,

    @Column(name = "address", nullable = false)
    val address: String,

    @Column(name = "document_ids", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    val documentIds: List<String> = emptyList(),

    @Column(name = "notes_id")
    val notesId: List<String>,

    @Column(name = "custom_tag_ids", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    val customTagIds: List<String> = emptyList(),

    @Column(name = "assignments", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    val assignments: List<CustomerAssignmentEntity> = emptyList(),

    @Column(name = "created_at", insertable = false, updatable = false)
    val createdAt: LocalDateTime? = null,

    @Column(name = "updated_at", insertable = false, updatable = false)
    val updatedAt: LocalDateTime? = null
)
