package com.oneplatform.backendcore.customer.integration.graphql

import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument
import com.oneplatform.backendcore.DgsConstants
import com.oneplatform.backendcore.core.auth.AuthenticatedUser
import com.oneplatform.backendcore.customer.domain.mapper.toGraphQL
import com.oneplatform.backendcore.customer.integration.service.CustomerIntegrationService
import com.oneplatform.backendcore.types.Customer
import com.oneplatform.backendcore.types.CustomerCreateAdditionalInformationInput
import com.oneplatform.backendcore.types.CustomerCreateBasicDetailsInput
import io.github.oshai.kotlinlogging.KotlinLogging

private val log = KotlinLogging.logger {}

@DgsComponent
class CustomerMutator(
    private val customerIntegrationService: CustomerIntegrationService,
    private val authUser: AuthenticatedUser
) {

    @DgsMutation(field = DgsConstants.MUTATION.CustomerCreateBasicDetails)
    fun customerCreateBasicDetails(@InputArgument input: CustomerCreateBasicDetailsInput): Customer {
        val user = authUser.getUser()
        if (user == null) {
            log.error { "User is not authenticated" }
            throw IllegalStateException("User is not authenticated")
        }
        log.info { "Received a request to create customer of type=${input.customerType}" }
        return customerIntegrationService.createCustomer(user, input).toGraphQL()
    }

    @DgsMutation(field = DgsConstants.MUTATION.CustomerCreateAdditionalInformation)
    fun customerCreateAdditionalInformation(@InputArgument input: CustomerCreateAdditionalInformationInput): Customer {
        val user = authUser.getUser()
        if (user == null) {
            log.error { "User is not authenticated" }
            throw IllegalStateException("User is not authenticated")
        }
        log.info { "Received a request to create additional information for customer=${input.}" }
        return customerIntegrationService.createAdditionalInformation(user, input).toGraphQL()
    }
}