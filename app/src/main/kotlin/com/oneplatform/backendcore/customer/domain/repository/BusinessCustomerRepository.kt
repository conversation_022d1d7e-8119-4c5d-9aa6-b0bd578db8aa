package com.oneplatform.backendcore.customer.domain.repository

import com.oneplatform.backendcore.customer.domain.model.customer.BusinessCustomer
import com.oneplatform.backendcore.types.Customer
import com.oneplatform.backendcore.types.GetCustomersQueryFilterInput

interface BusinessCustomerRepository {
    fun save(customer: BusinessCustomer): BusinessCustomer
    fun findAllWithSpec(filter: GetCustomersQueryFilterInput, companyId: String): List<com.oneplatform.backendcore.customer.domain.model.customer.Customer>
    fun findById(id: String): com.oneplatform.backendcore.customer.domain.model.customer.Customer?
}