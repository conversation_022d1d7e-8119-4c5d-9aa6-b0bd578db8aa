package com.oneplatform.backendcore.customer.domain.repository

import com.oneplatform.backendcore.customer.domain.model.customer.IndividualCustomer
import com.oneplatform.backendcore.types.Customer
import com.oneplatform.backendcore.types.GetCustomersQueryFilterInput

interface IndividualCustomerRepository {
    fun save(customer: IndividualCustomer): IndividualCustomer
    fun findAllWithSpec(filter: GetCustomersQueryFilterInput, companyId: String): List<com.oneplatform.backendcore.customer.domain.model.customer.Customer>
    fun findById(id: String): com.oneplatform.backendcore.customer.domain.model.customer.Customer?
}