package com.oneplatform.backendcore.core.domain.service

import com.oneplatform.backendcore.core.domain.model.notes.Notes
import com.oneplatform.backendcore.core.integration.repository.service.NotesRepositoryService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class NotesDomainService(
    private val notesRepositoryService: NotesRepositoryService
) {

    fun getNotesById(notesId: String?, companyId: String): Notes? {
        if (notesId == null) {
            log.info { "No notes ID provided for company=$companyId" }
            return null
        }
        val notes = notesRepositoryService.findById(notesId)
        return notes
    }
}
