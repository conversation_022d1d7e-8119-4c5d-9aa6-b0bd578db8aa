package com.oneplatform.backendcore.core.integration.mapper

import com.oneplatform.backendcore.core.integration.entity.JpaNotesEntity
import com.oneplatform.backendcore.types.Notes

fun Notes.toEntity(): JpaNotesEntity {
    return JpaNotesEntity(
        content = this.content,
        tags = this.tags
    )
}

fun JpaNotesEntity.toDomain(): com.oneplatform.backendcore.core.domain.model.notes.Notes {
    return com.oneplatform.backendcore.core.domain.model.notes.Notes(
        id = this.id,
        content = this.content,
        tags = this.tags
    )
}

fun JpaNotesEntity.toGraphQL(): Notes {
    return Notes(
        id = this.id ?: "",
        content = this.content,
        tags = this.tags
    )
}

fun com.oneplatform.backendcore.core.domain.model.notes.Notes.toGraphQL(): Notes {
    return Notes(
        id = this.id ?: run { "" },
        content = this.content,
        tags = this.tags
    )
}

fun Notes.toDomain(): com.oneplatform.backendcore.core.domain.model.notes.Notes {
    return com.oneplatform.backendcore.core.domain.model.notes.Notes(
        id = this.id,
        content = this.content,
        tags = this.tags
    )
}