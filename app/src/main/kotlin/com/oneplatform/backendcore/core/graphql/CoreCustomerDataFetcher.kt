package com.oneplatform.backendcore.core.graphql

import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsData
import com.oneplatform.backendcore.DgsConstants
import com.oneplatform.backendcore.core.auth.AuthenticatedUser
import com.oneplatform.backendcore.core.integration.mapper.toGraphQL
import com.oneplatform.backendcore.core.integration.service.CoreIntegrationService
import com.oneplatform.backendcore.types.*
import graphql.schema.DataFetchingEnvironment
import io.github.oshai.kotlinlogging.KotlinLogging

private val log = KotlinLogging.logger {}

@DgsComponent
class CoreCustomerDataFetcher(
    private val coreIntegrationService: CoreIntegrationService,
    private val authUser: AuthenticatedUser
) {

    @DgsData(parentType = DgsConstants.CUSTOMERBUSINESS.TYPE_NAME, field = DgsConstants.CUSTOMERBUSINESS.Documents)
    fun getBusinessCustomerDocuments(dfe: DataFetchingEnvironment): List<Document> {
        val customerBusiness = dfe.getSource<CustomerBusiness>()
        val documents = coreIntegrationService.getDocumentsForCustomer(customerBusiness!!, authUser)
        return documents.map { it.toGraphQL() }
    }

    @DgsData(parentType = DgsConstants.CUSTOMERINDIVIDUAL.TYPE_NAME, field = DgsConstants.CUSTOMERINDIVIDUAL.Documents)
    fun getIndividualCustomerDocuments(dfe: DataFetchingEnvironment): List<Document> {
        val customerIndividual = dfe.getSource<CustomerIndividual>()
        val documents = coreIntegrationService.getDocumentsForCustomer(customerIndividual!!, authUser)
        return documents.map { it.toGraphQL() }
    }

    @DgsData(parentType = DgsConstants.CUSTOMERBUSINESS.TYPE_NAME, field = DgsConstants.CUSTOMERBUSINESS.Notes)
    fun getBusinessCustomerNotes(dfe: DataFetchingEnvironment): Notes? {
        val customerBusiness = dfe.getSource<CustomerBusiness>()
        val notes = coreIntegrationService.getNotesForCustomer(customerBusiness!!, authUser)
        return notes?.toGraphQL()
    }

    @DgsData(parentType = DgsConstants.CUSTOMERINDIVIDUAL.TYPE_NAME, field = DgsConstants.CUSTOMERINDIVIDUAL.Notes)
    fun getIndividualCustomerNotes(dfe: DataFetchingEnvironment): Notes? {
        val customerIndividual = dfe.getSource<CustomerIndividual>()
        val notes = coreIntegrationService.getNotesForCustomer(customerIndividual!!, authUser)
        return notes?.toGraphQL()
    }

    @DgsData(parentType = DgsConstants.CUSTOMERBUSINESS.TYPE_NAME, field = DgsConstants.CUSTOMERBUSINESS.CustomTags)
    fun getBusinessCustomerCustomTags(dfe: DataFetchingEnvironment): List<CustomTag> {
        val customerBusiness = dfe.getSource<CustomerBusiness>()
        val customTags = coreIntegrationService.getCustomTagsForCustomer(customerBusiness!!, authUser)
        return customTags.map { it.toGraphQL() }
    }

    @DgsData(parentType = DgsConstants.CUSTOMERINDIVIDUAL.TYPE_NAME, field = DgsConstants.CUSTOMERINDIVIDUAL.CustomTags)
    fun getIndividualCustomerCustomTags(dfe: DataFetchingEnvironment): List<CustomTag> {
        val customerIndividual = dfe.getSource<CustomerIndividual>()
        val customTags = coreIntegrationService.getCustomTagsForCustomer(customerIndividual!!, authUser)
        return customTags.map { it.toGraphQL() }
    }
}