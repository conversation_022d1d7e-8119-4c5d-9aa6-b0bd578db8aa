package com.oneplatform.backendcore.core.integration.mapper

import com.oneplatform.backendcore.core.domain.model.document.DocumentExternalSystem
import com.oneplatform.backendcore.core.integration.entity.JpaDocumentEntity
import com.oneplatform.backendcore.types.Document
import com.oneplatform.backendcore.types.FileLink

fun Document.toEntity(): JpaDocumentEntity {
    return JpaDocumentEntity(
        externalSystem = DocumentExternalSystem.S3,
        externalId = this.id // Use the document ID as external ID for now
    )
}

fun JpaDocumentEntity.toDomain(): com.oneplatform.backendcore.core.domain.model.document.Document {
    return com.oneplatform.backendcore.core.domain.model.document.Document(
        id = this.id!!,
        file = FileLink(
            signedReadURL = "https://google.com",
            signedWriteURL = "https://google.com"
        )
    )
}

fun JpaDocumentEntity.toGraphQL(): Document {
    return Document(
        id = this.id!!,
        file = FileLink(
            signedReadURL = "https://google.com",
            signedWriteURL = "https://google.com"
        )
    )
}


fun com.oneplatform.backendcore.core.domain.model.document.Document.toGraphQL(): Document {
    return Document(
        id = this.id,
        file = FileLink(
            signedReadURL = "somefile link for ${this.id}"
        )
    )
}

fun Document.toDomain(): com.oneplatform.backendcore.core.domain.model.document.Document {
    return com.oneplatform.backendcore.core.domain.model.document.Document(
        id = this.id,
    )
}