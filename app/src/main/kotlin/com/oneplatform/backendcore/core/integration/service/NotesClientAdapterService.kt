package com.oneplatform.backendcore.core.integration.service

import com.oneplatform.backendcore.core.client.NotesClientAdapter
import com.oneplatform.backendcore.core.integration.mapper.toGraphQL
import com.oneplatform.backendcore.core.integration.repository.service.NotesRepositoryService
import com.oneplatform.backendcore.types.Notes
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class NotesClientAdapterService(
    private val notesRepositoryService: NotesRepositoryService
) : NotesClientAdapter {
    override fun createNotes(notes: Notes): Notes {
        log.info { "Creating notes with content length=${notes.content.length}" }
        return notesRepositoryService
            .save(notes)
            .toGraphQL()
    }
}