package com.oneplatform.backendcore.core.domain.service

import com.oneplatform.backendcore.core.domain.model.document.Document
import com.oneplatform.backendcore.core.exceptions.CoreDomainException
import com.oneplatform.backendcore.core.exceptions.GraphQLErrorCode
import com.oneplatform.backendcore.core.integration.repository.service.DocumentRepositoryService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class DocumentDomainService(
    private val documentRepositoryService: DocumentRepositoryService
) {

    fun getDocumentsByIds(documentIds: List<String>, companyId: String): List<Document> {
        log.info { "Fetching ${documentIds.size} documents for company=$companyId" }
        val documents = documentRepositoryService.findByIdIn(documentIds)
        if (documents.size != documentIds.size) {
            val foundIds = documents.map { it.id }.toSet()
            val missingIds = documentIds.filterNot { foundIds.contains(it) }
            log.warn { "Some documents not found for company=$companyId. Missing IDs: $missingIds" }
        }
        log.info { "Successfully retrieved ${documents.size} documents for company=$companyId" }
        return documents
    }
}
