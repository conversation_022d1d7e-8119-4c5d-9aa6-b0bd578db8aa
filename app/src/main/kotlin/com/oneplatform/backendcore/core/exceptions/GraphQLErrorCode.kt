package com.oneplatform.backendcore.core.exceptions

enum class GraphQL<PERSON><PERSON>rCode {
    UNAUT<PERSON>ENTICATED,
    UNAUT<PERSON><PERSON><PERSON><PERSON>ED,
    COMPANY_NOT_FOUND_FOR_USER,
    COMPANY_NOT_FOUND_FOR_COMPANY_ID,
    COMPANY_BASIC_DETAILS_NOT_FOUND,
    COMPANY_IS_INACTIVE,
    COMPANY_USER_NOT_FOUND,
    COMPANY_USER_IS_INACTIVE,
    CUSTOMER_TYPE_UNDEFINED,
    CUSTOMER_GENERIC_EXCEPTION,
    CORE_DOCUMENTS_NOT_FOUND,
    CORE_NOTES_NOT_FOUND,
    CORE_CUSTOM_TAGS_NOT_FOUND,
    CORE_ENTITY_ACCESS_DENIED,
    CORE_DATA_FETCHER_ERROR
}