package com.oneplatform.backendcore.core.integration.repository.service

import com.oneplatform.backendcore.core.integration.entity.JpaNotesEntity
import com.oneplatform.backendcore.core.integration.mapper.toEntity
import com.oneplatform.backendcore.core.integration.mapper.toDomain
import com.oneplatform.backendcore.core.integration.repository.interfaces.NotesJpaRepository
import com.oneplatform.backendcore.types.Notes
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Component
class NotesRepositoryService(
    private val notesJpaRepository: NotesJpaRepository
) {

    fun save(notes: Notes): JpaNotesEntity {
        log.info { "Saving notes with content length=${notes.content.length}" }
        val entity = notes.toEntity()
        val savedEntity = notesJpaRepository.save(entity)
        log.info { "Notes successfully saved with id=${savedEntity.id}" }
        return savedEntity
    }

    fun findById(id: String): com.oneplatform.backendcore.core.domain.model.notes.Notes? {
        log.debug { "Finding notes by id=$id" }
        return notesJpaRepository.findById(id).orElse(null)?.toDomain()
    }
}
