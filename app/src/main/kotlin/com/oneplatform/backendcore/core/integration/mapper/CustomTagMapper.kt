package com.oneplatform.backendcore.core.integration.mapper

import com.oneplatform.backendcore.core.integration.entity.JpaCustomTagEntity
import com.oneplatform.backendcore.types.CustomTag
import com.oneplatform.backendcore.types.CustomTagType

fun CustomTag.toEntity(): JpaCustomTagEntity {
    return JpaCustomTagEntity(
        key = this.key,
        label = this.label,
        value = this.value,
        type = this.type,
        description = this.description
    )
}

fun JpaCustomTagEntity.toDomain(): com.oneplatform.backendcore.core.domain.model.customtag.CustomTag {
    return com.oneplatform.backendcore.core.domain.model.customtag.CustomTag(
        id = this.id,
        key = this.key,
        label = this.label,
        value = this.value,
        type = this.type,
        description = this.description
    )
}

fun JpaCustomTagEntity.toGraphQL(): CustomTag {
    return CustomTag(
        id = this.id ?: run { "" },
        key = this.key,
        label = this.label,
        value = this.value,
        type = this.type,
        description = this.description
    )
}

fun com.oneplatform.backendcore.core.domain.model.customtag.CustomTag.toGraphQL(): CustomTag {
    return CustomTag(
        id = this.id ?: run { "" },
        key = this.key,
        label = this.label,
        value = this.value,
        type = this.type,
        description = this.description
    )
}

fun CustomTag.toDomain(): com.oneplatform.backendcore.core.domain.model.customtag.CustomTag {
    return com.oneplatform.backendcore.core.domain.model.customtag.CustomTag(
        id = this.id,
        key = this.key,
        label = this.label,
        value = this.value,
        type = this.type,
        description = this.description
    )
}