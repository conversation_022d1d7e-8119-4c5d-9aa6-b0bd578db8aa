package com.oneplatform.backendcore.core.integration.service

import com.oneplatform.backendcore.company.client.`interface`.CompanyClientAdapter
import com.oneplatform.backendcore.company.client.`interface`.CompanyUserClientAdapter
import com.oneplatform.backendcore.company.exception.CompanyNotFoundException
import com.oneplatform.backendcore.core.auth.AuthenticatedUser
import com.oneplatform.backendcore.core.domain.model.customtag.CustomTag
import com.oneplatform.backendcore.core.domain.model.document.Document
import com.oneplatform.backendcore.core.domain.model.notes.Notes
import com.oneplatform.backendcore.core.domain.service.CustomTagDomainService
import com.oneplatform.backendcore.core.domain.service.DocumentDomainService
import com.oneplatform.backendcore.core.domain.service.NotesDomainService
import com.oneplatform.backendcore.core.exceptions.CoreDomainException
import com.oneplatform.backendcore.core.exceptions.GraphQLErrorCode
import com.oneplatform.backendcore.types.*
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class CoreIntegrationService(
    private val documentDomainService: DocumentDomainService,
    private val notesDomainService: NotesDomainService,
    private val customTagDomainService: CustomTagDomainService,
    private val companyClientAdapter: CompanyClientAdapter,
    private val companyUserClientAdapter: CompanyUserClientAdapter
) {

    fun getDocumentsForCustomer(
        customer: Customer,
        authUser: AuthenticatedUser
    ): List<Document> {
        log.info { "Fetching documents for customer=${customer.id} with ${customer.documents.size} document IDs" }
        validateUserAccess(customer.company.id, authUser)
        val toFetchDocuments = customer.documents.mapNotNull { it?.id }
        return documentDomainService.getDocumentsByIds(toFetchDocuments, customer.company.id)
    }

    fun getNotesForCustomer(
        customer: Customer,
        authUser: AuthenticatedUser
    ): Notes? {
        log.info { "Fetching notes for customer=${customer.id} for userID=${authUser.getUser()!!.id}" }
        validateUserAccess(customer.company.id, authUser)
        return notesDomainService.getNotesByIds(customer.notes, customer.company.id)
    }

    fun getCustomTagsForCustomer(
        customer: Customer,
        authUser: AuthenticatedUser
    ): List<CustomTag> {
        log.info { "Fetching custom tags for customer=${customer.id} for userID=${authUser.getUser()!!.id}" }
        validateUserAccess(customer.company.id, authUser)
        val customTagIdsToFetch = customer.customTags.mapNotNull { it?.id }
        return customTagDomainService.getCustomTagsByIds(customTagIdsToFetch, customer.company.id)
    }

    private fun validateUserAccess(
        customerCompanyId: String,
        authUser: AuthenticatedUser,
    ) {
        val user = authUser.getUser()!!

        val companyUser = companyUserClientAdapter.getCompanyUserForUserID(user.id)
        val company = companyClientAdapter.getCompanyForID(companyUser.company.id)
        if (company.status == CompanyStatus.INACTIVE) {
            log.error { "Company is inactive for companyID=${company.id} when fetching core data for userID=${user.id}" }
            throw CompanyNotFoundException(
                "Company is inactive for companyID=${company.id}",
                graphQLErrorCode = GraphQLErrorCode.COMPANY_IS_INACTIVE
            )
        }

        if (customerCompanyId != company.id) {
            log.error { "User does not have access to core data for userID=${user.id}. User company=${company.id}, customer company=$customerCompanyId" }
            throw CoreDomainException(
                "User does not have access to core data for userID=${user.id}. User company=${company.id}, customer company=$customerCompanyId",
                graphQLErrorCode = GraphQLErrorCode.CORE_ENTITY_ACCESS_DENIED
            )
        }
    }
}
