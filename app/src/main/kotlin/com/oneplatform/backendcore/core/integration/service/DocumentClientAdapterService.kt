package com.oneplatform.backendcore.core.integration.service

import com.oneplatform.backendcore.core.client.DocumentClientAdapter
import com.oneplatform.backendcore.core.integration.mapper.toDomain
import com.oneplatform.backendcore.core.integration.mapper.toGraphQL
import com.oneplatform.backendcore.core.integration.repository.service.DocumentRepositoryService
import com.oneplatform.backendcore.types.Document
import com.oneplatform.backendcore.types.Notes
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class DocumentClientAdapterService(
    private val documentRepositoryService: DocumentRepositoryService
) : DocumentClientAdapter {

    override fun createDocuments(documents: List<Document>): List<Document> {
        log.info { "Creating documents with count=${documents.size}" }
        return documents.map { documentRepositoryService.save(it).toGraphQL() }
    }
}