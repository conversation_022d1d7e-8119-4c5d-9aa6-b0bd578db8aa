package com.oneplatform.backendcore.core.integration.repository.service

import com.oneplatform.backendcore.core.integration.entity.JpaCustomTagEntity
import com.oneplatform.backendcore.core.integration.mapper.toDomain
import com.oneplatform.backendcore.core.integration.mapper.toEntity
import com.oneplatform.backendcore.core.integration.mapper.toGraphQL
import com.oneplatform.backendcore.core.integration.repository.interfaces.CustomTagJpaRepository
import com.oneplatform.backendcore.types.CustomTag
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Component
class CustomTagRepositoryService(
    private val customTagJpaRepository: CustomTagJpaRepository
) {

    fun save(customTag: CustomTag): JpaCustomTagEntity {
        log.info { "Saving custom tag with key=${customTag.key}" }
        val entity = customTag.toEntity()
        val savedEntity = customTagJpaRepository.save(entity)
        log.info { "Custom tag successfully saved with id=${savedEntity.id}" }
        return savedEntity
    }

    fun findByIdIn(ids: List<String>): List<com.oneplatform.backendcore.core.domain.model.customtag.CustomTag> {
        if (ids.isEmpty()) {
            return emptyList()
        }
        val entities = customTagJpaRepository.findAllById(ids)
        return entities.map { it.toDomain() }
    }

    fun findById(id: String): CustomTag? {
        log.debug { "Finding custom tag by id=$id" }
        return customTagJpaRepository.findById(id).orElse(null)?.toGraphQL()
    }
}
