package com.oneplatform.backendcore.core.integration.service

import com.oneplatform.backendcore.core.client.CustomTagClientAdapter
import com.oneplatform.backendcore.core.integration.mapper.toGraphQL
import com.oneplatform.backendcore.core.integration.repository.service.CustomTagRepositoryService
import com.oneplatform.backendcore.types.CustomTag
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class CustomTagClientAdapterService(
    private val customTagRepositoryService: CustomTagRepositoryService
) : CustomTagClientAdapter {

    override fun createCustomTags(customTags: List<CustomTag>): List<CustomTag> {
        log.info { "Creating custom tags with count=${customTags.size}" }
        return customTags.map { customTagRepositoryService.save(it).toGraphQL() }
    }
}