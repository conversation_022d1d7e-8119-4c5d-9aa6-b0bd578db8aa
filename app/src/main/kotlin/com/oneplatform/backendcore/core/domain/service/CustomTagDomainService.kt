package com.oneplatform.backendcore.core.domain.service

import com.oneplatform.backendcore.core.integration.repository.service.CustomTagRepositoryService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class CustomTagDomainService(
    private val customTagRepositoryService: CustomTagRepositoryService
) {

    fun getCustomTagsByIds(
        customTagIds: List<String>,
        companyId: String
    ): List<com.oneplatform.backendcore.core.domain.model.customtag.CustomTag> {
        if (customTagIds.isEmpty()) {
            return emptyList()
        }
        log.info { "Fetching ${customTagIds.size} custom tags for company=$companyId" }
        val customTags = customTagRepositoryService.findByIdIn(customTagIds)
        if (customTags.size != customTagIds.size) {
            val foundIds = customTags.map { it.id }.toSet()
            val missingIds = customTagIds.filterNot { foundIds.contains(it) }
            log.warn { "Some custom tags not found for company=$companyId. Missing IDs: $missingIds" }
        }
        log.info { "Successfully retrieved ${customTags.size} custom tags for company=$companyId" }
        return customTags
    }
}
