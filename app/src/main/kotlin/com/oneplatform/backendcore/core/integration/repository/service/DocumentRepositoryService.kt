package com.oneplatform.backendcore.core.integration.repository.service

import com.oneplatform.backendcore.core.integration.entity.JpaDocumentEntity
import com.oneplatform.backendcore.core.integration.mapper.toDomain
import com.oneplatform.backendcore.core.integration.mapper.toEntity
import com.oneplatform.backendcore.core.integration.mapper.toGraphQL
import com.oneplatform.backendcore.core.integration.repository.interfaces.DocumentJpaRepository
import com.oneplatform.backendcore.types.Document
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Component
class DocumentRepositoryService(
    private val documentJpaRepository: DocumentJpaRepository
) {

    fun save(document: Document): JpaDocumentEntity {
        log.info { "Saving document with id=${document.id}" }
        val entity = document.toEntity()
        val savedEntity = documentJpaRepository.save(entity)
        log.info { "Document successfully saved with id=${savedEntity.id}" }
        return savedEntity
    }

    fun findByIdIn(ids: List<String>): List<com.oneplatform.backendcore.core.domain.model.document.Document> {
        if (ids.isEmpty()) {
            return emptyList()
        }
        log.info { "Finding documents by IDs: $ids from db" }
        val entities = documentJpaRepository.findAllById(ids)
        return entities.map { it.toDomain() }
    }

    fun findById(id: String): Document? {
        log.debug { "Finding document by id=$id" }
        return documentJpaRepository.findById(id).orElse(null)?.toGraphQL()
    }
}
